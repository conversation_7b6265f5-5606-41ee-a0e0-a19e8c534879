<script setup lang="ts" name="channel">
import { ref, reactive, onMounted, onActivated } from "vue";
import { ElMessage } from "element-plus";
import { useAppStoreHook } from "/@/store/modules/app";
import { useUserStoreHook } from "/@/store/modules/user";
import { useRenderIcon } from "/@/components/ReIcon/src/hooks";
import {
  getChannelList,
  getTag,
  updateStatusChannel,
  getAllRuleConfig,
  getListWeChat,
  getListConfig
} from "/@/api/active";
import paramsHandle from "/@/utils/handle/paramsHandle";
import RePagination from "/@/components/RePagination/index.vue";
import ReTable from "/@/components/ReTable/index.vue";
import ReCardList from "/@/components/ReCardList/index.vue";
import {
  tagList,
  listHeader,
  ruleTemplateList,
  setMiniList,
  setTypeList,
  setConfigList,
  getWechat
} from "./utils/listHeader";
import { useDetail } from "./utils/toDetails";
import baseURL from "/@/api/url";
import filterChanges from "/@/utils/handle/filterChanges";
import { getGroupListApi } from "/@/api/active/channelType";
import { getWeChatListApi } from "/@/api/daily/weChatWork";
import { useWechatTag } from "/@/hooks/business/useWechatTag";

const { allTag, getAllTag } = useWechatTag();

const { toDetail } = useDetail();

let device = useAppStoreHook().device;

let operation = [
  { event: "details", text: "查看详情" },
  { event: "edit", text: "编辑" },
  { event: "copyLink", text: "复制链接" },
  {
    event: "up",
    text: "上线",
    isShow: row => row.status === 2,
    popconfirm: "确定上线吗？"
  },
  {
    event: "down",
    text: "下线",
    isShow: row => row.status === 1,
    popconfirm: "确定下线吗？"
  }
];

useUserStoreHook().authorizationMap.indexOf(
  "telesale_admin_channelCode_set"
) === -1 && operation.splice(1, 1);

//带分页列表数据必备
const loading = ref(true);
const dataList = ref([]);
const total = ref(0);

//分页
const rePaginationRefs = ref();
function onSearch() {
  rePaginationRefs.value.onSearch();
}

//表头筛选
function filterChange(row) {
  filterChanges(
    row,
    [
      { name: "status", val: undefined },
      { name: "miniProgramId", val: undefined },
      { name: "channelGroup", val: undefined },
      { name: "accountId", val: undefined }
    ],
    form
  );
  onSearch();
}

const form = reactive({
  channel: "",
  status: 1,
  ruleTemplateId: "",
  firstConfigurationId: [],
  multipleConfigurationId: []
});

let qrCodeAddCountTotal = ref(0);
let todayQRCodeAddCountTotal = ref(0);
const configOptions = ref([]);

const isOnce = ref(true);

const getTypeList = async () => {
  const res = await getGroupListApi();
  setTypeList(res.data.list);
};
getTypeList();

function getList() {
  loading.value = true;
  getChannelList(
    paramsHandle(form, {
      zero: ["ruleTemplateId"],
      pageIndex: rePaginationRefs.value.pageIndex,
      pageSize: rePaginationRefs.value.pageSize
    })
  )
    .then(({ data }: { data: any }) => {
      dataList.value = data.list;
      total.value = data.total;
      qrCodeAddCountTotal.value = data.qrCodeAddCountTotal || 0;
      todayQRCodeAddCountTotal.value = data.todayQRCodeAddCountTotal || 0;
      loading.value = false;
    })
    .catch(() => {
      dataList.value = [];
      total.value = 0;
      qrCodeAddCountTotal.value = 0;
      todayQRCodeAddCountTotal.value = 0;
      loading.value = false;
    });
}
function getTagMath() {
  loading.value = true;
  return Promise.all([getAllRuleConfig()])
    .then((res: any) => {
      ruleTemplateList.value.length = 0;
      res[0].data.list.forEach(item => ruleTemplateList.value.push(item));
      return;
    })
    .catch(() => {
      return;
    });
}

function add() {
  toDetail({ type: "add" });
}

function edit(row) {
  toDetail({ type: "edit", id: row.id + "" });
}

function details(row) {
  toDetail({ type: "detail", id: row.id + "" });
}

function copyLink(row) {
  let link = `${
    import.meta.env.VITE_DX_HOST
  }/miniprogram/qrcode/continue?topicId=0&channelId=${row.id}&phone=&uid=`;
  const input = document.createElement("input");
  input.setAttribute("value", link);
  document.body.appendChild(input);
  input.select();
  document.execCommand("copy") && ElMessage.success("复制成功！");
  document.body.removeChild(input);
}

function changeState(row, status) {
  loading.value = true;
  updateStatusChannel({ id: row.id, status: status })
    .then(() => {
      ElMessage.success("操作成功");
      getList();
    })
    .catch(() => {
      loading.value = false;
    });
}

function parantMath({ key, params }) {
  switch (key) {
    case "edit":
      edit(params);
      break;
    case "details":
      details(params);
      break;
    case "copyLink":
      copyLink(params);
      break;
    case "up":
      changeState(params, 1);
      break;
    case "down":
      changeState(params, 2);
      break;
  }
}

onMounted(async () => {
  await getTagMath();
  await getWechat();
  await getAllTag();
  tagList.value = allTag.value;
  const res = await getListWeChat();
  setMiniList(res?.data?.list);
  // 获取活码主题配置列表
  try {
    const configRes = await getListConfig({});
    configOptions.value = (configRes?.data?.list || []).map(item => ({
      label: item.name,
      value: item.id
    }));
    setConfigList(configRes?.data?.list);
  } catch (error) {
    console.error("获取活码主题配置失败:", error);
  }
  getList();
  isOnce.value = false;
});

onActivated(() => {
  if (!isOnce.value) {
    getList();
  }
});
</script>

<template>
  <div class="g-margin-20" v-loading="loading">
    <el-card>
      <el-form
        ref="formRef"
        :inline="true"
        :model="form"
        class="clearfix"
        @submit.prevent
      >
        <el-form-item prop="channel">
          <el-input
            v-model="form.channel"
            placeholder="请输入场景名称"
            clearable
            @keyup.enter="onSearch"
          />
        </el-form-item>
        <el-form-item prop="ruleTemplateId">
          <el-select
            v-model="form.ruleTemplateId"
            placeholder="请选择分配规则"
            clearable
            filterable
            @keyup.enter="getList"
          >
            <el-option
              v-for="item in ruleTemplateList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item prop="firstConfigurationId">
          <el-select
            v-model="form.firstConfigurationId"
            placeholder="请选择首次活码主题"
            clearable
            filterable
            multiple
            @keyup.enter="getList"
          >
            <el-option
              v-for="item in configOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item prop="multipleConfigurationId">
          <el-select
            v-model="form.multipleConfigurationId"
            placeholder="请选择多次活码主题"
            clearable
            filterable
            multiple
            @keyup.enter="getList"
          >
            <el-option
              v-for="item in configOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            :icon="useRenderIcon('search')"
            @click="onSearch"
          >
            搜索
          </el-button>
        </el-form-item>
        <el-form-item class="g-set-button">
          <el-button
            type="primary"
            @click="add"
            v-if="
              useUserStoreHook().authorizationMap.indexOf(
                'telesale_admin_channelCode_set'
              ) > -1
            "
          >
            新增活码场景
          </el-button>
        </el-form-item>
      </el-form>
      <div class="g-list-box">
        <div>
          <span class="title-name">所有渠道累计获得客户数：</span>
          <span class="text-name">{{ qrCodeAddCountTotal }}</span>
        </div>
        <div>
          <span class="title-name">所有渠道今日获得客户数：</span>
          <span class="text-name">{{ todayQRCodeAddCountTotal }}</span>
        </div>
      </div>
      <div class="g-table-box">
        <ReTable
          v-if="device !== 'mobile'"
          ref="tableRefs"
          :dataList="dataList"
          :listHeader="listHeader"
          :operation="operation"
          @parantMath="parantMath"
          :widthOperation="150"
          :filterChange="filterChange"
        />
        <ReCardList
          v-else
          ref="cardRefs"
          :dataList="dataList"
          :listHeader="listHeader"
          :operation="operation"
          @parantMath="parantMath"
        />
      </div>
      <RePagination ref="rePaginationRefs" :total="total" @getList="getList" />
    </el-card>
  </div>
</template>
