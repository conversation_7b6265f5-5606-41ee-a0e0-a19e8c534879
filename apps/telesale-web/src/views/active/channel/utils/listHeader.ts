import { ref, computed } from "vue";
import filterDataChange from "/@/utils/handle/filterDataChange";
import { TableColumns } from "/@/components/ReTable/types";
import { getLabel } from "/@/utils/common";
import { getWeChatListApi } from "/@/api/daily/weChatWork";

const tagList = ref([]);
const ruleTemplateList = ref<any[]>([]);
const miniList = ref([]);
const typeList = ref([]);
const wechatList = ref([]);
const configList = ref([]);

const setTypeList = val => {
  typeList.value = val;
};

const setMiniList = val => {
  miniList.value = val;
};

const setConfigList = val => {
  configList.value = val?.map(item => ({
    text: item.name,
    value: item.id
  })) || [];
};

const getWechat = async () => {
  const res = await getWeChatListApi();
  wechatList.value = res.data.list?.map(item => {
    return {
      text: item.name,
      value: item.id
    };
  });
};

function tagHandle(row) {
  let names = "";
  row.tag.forEach((id, i) => {
    let hasName = tagList.value.some(item => {
      const index = item.tag.findIndex(tag => tag.id === id);
      if (index > -1) {
        names +=
          i === row.tag.length - 1
            ? item.tag[index]?.name
            : item.tag[index]?.name + "，";
        return true;
      }
    });
    !hasName && (hasName += i === row.tag.length - 1 ? id : id + "，");
  });
  return names;
}

const statusList = [
  {
    id: 1,
    name: "上线"
  },
  {
    id: 2,
    name: "下线"
  }
];

const listHeader = computed<TableColumns[]>(() => {
  return [
    { field: "id", desc: "ID", minWidth: 60 },
    { field: "name", desc: "场景名称", minWidth: 120 },
    {
      field: "tags",
      desc: "标签",
      minWidth: 120,
      filters: tagHandle,
      showTip: true
    },
    // { field: "remark", desc: "备注", minWidth: 120 },
    {
      field: "status",
      desc: "状态",
      typeChange: statusList,
      filtersList: filterDataChange(statusList, "name", "id"),
      filteredValue: 1
    },
    {
      field: "ruleTemplateId",
      desc: "分配规则名称",
      typeChange: ruleTemplateList.value,
      minWidth: 120
    },
    {
      field: "miniProgramId",
      desc: "使用小程序",
      filtersList: filterDataChange(miniList.value, "name", "id"),
      filters: row => row?.miniProgram?.name || "",
      minWidth: 120
    },
    {
      field: "channelGroup",
      desc: "类型",
      filtersList: filterDataChange(typeList.value, "name", "id"),
      filters: row => getLabel(row?.channelGroup, typeList.value, "name", "id"),
      minWidth: 120
    },
    {
      field: "accountId",
      desc: "所属企微账号",
      filtersList: wechatList.value,
      customRender: ({ text }) => {
        return getLabel(text, wechatList.value, "text");
      }
    },
    { field: "addCustomerCount", desc: "累计获得客户数", minWidth: 120 },
    {
      field: "todayAddCustomerCountWithQRCodeId",
      desc: "今日获得客户数",
      minWidth: 120
    },
    {
      field: "firstConfigurationName",
      desc: "首次活码主题",
      minWidth: 120,
      filters: row => row?.firstConfiguration?.name || "-",
      filtersList: configList.value
    },
    {
      field: "multipleConfigurationName",
      desc: "多次活码主题",
      minWidth: 120,
      filters: row => row?.multipleConfiguration?.name || "-",
      filtersList: configList.value
    },
    { field: "createdAt", desc: "创建时间", minWidth: 100, timeChange: 3 }
  ];
});

export {
  tagList,
  listHeader,
  ruleTemplateList,
  setMiniList,
  setTypeList,
  setConfigList,
  getWechat
};
