<script setup lang="ts">
import { ref, reactive, computed } from "vue";
import UITemplate from "../../uiConfigDetails/components/UITemplate.vue";
import { getPic } from "/@/api/order";

interface Props {
  firstConfig: any;
  multipleConfig: any;
  loading: boolean;
  type: string;
}
const props = defineProps<Props>();

interface Emits {
  (e: "update:firstConfig", val: any): void;
  (e: "update:multipleConfig", val: any): void;
  (e: "update:loading", val: boolean): void;
}

const emit = defineEmits<Emits>();

const firstConfig = computed({
  get() {
    return props.firstConfig;
  },
  set(val: any) {
    emit("update:firstConfig", val);
  }
});

const multipleConfig = computed({
  get() {
    return props.multipleConfig;
  },
  set(val: any) {
    emit("update:multipleConfig", val);
  }
});

const loading = computed({
  get() {
    return props.loading;
  },
  set(val: boolean) {
    emit("update:loading", val);
  }
});
const firstPicUrl = reactive({
  background: "",
  button: ""
});

const multiplePicUrl = reactive({
  background: "",
  button: ""
});

//获取图片
function getPicMath(val, key, configType = "first") {
  const targetPicUrl = configType === "first" ? firstPicUrl : multiplePicUrl;
  key === "background" && (loading.value = true);
  getPic(val)
    .then(({ data }: { data: any }) => {
      targetPicUrl[key] = data;
      key === "background" && (loading.value = false);
    })
    .catch(() => {
      key === "background" && (loading.value = false);
    });
}

function changeFirstConfig(val) {
  if (!val) return;
  getPicMath(val.background, "background", "first");
  if (val.button) {
    getPicMath(val.button, "button", "first");
  } else {
    firstPicUrl.button = "";
  }
}

function changeMultipleConfig(val) {
  if (!val) return;
  getPicMath(val.background, "background", "multiple");
  if (val.button) {
    getPicMath(val.button, "button", "multiple");
  } else {
    multiplePicUrl.button = "";
  }
}

let configList = ref([]);

defineExpose({
  configList,
  changeFirstConfig,
  changeMultipleConfig
});
</script>

<template>
  <el-form-item label="首次活码主题：" prop="firstConfig">
    <el-select
      v-model="firstConfig"
      filterable
      clearable
      value-key="id"
      :disabled="type === 'detail'"
      @change="changeFirstConfig"
    >
      <el-option
        v-for="item in configList"
        :label="item.name"
        :value="item"
        :key="item.id"
      />
    </el-select>
  </el-form-item>
  <el-form-item label="多次活码主题：">
    <el-select
      v-model="multipleConfig"
      filterable
      clearable
      value-key="id"
      :disabled="type === 'detail'"
      @change="changeMultipleConfig"
    >
      <el-option
        v-for="item in configList"
        :label="item.name"
        :value="item"
        :key="item.id"
      />
    </el-select>
  </el-form-item>
  <el-form-item label="首次配置页展示：" style="max-height: 360px" v-if="firstConfig">
    <UITemplate
      ref="firstUITemplateRef"
      :picUrl="firstPicUrl"
      :bottomColor="firstConfig?.bottomColor"
      :navigationFontColor="firstConfig?.navigationFontColor"
      type="small"
    />
  </el-form-item>
  <el-form-item label="多次配置页展示：" style="max-height: 360px" v-if="multipleConfig">
    <UITemplate
      ref="multipleUITemplateRef"
      :picUrl="multiplePicUrl"
      :bottomColor="multipleConfig?.bottomColor"
      :navigationFontColor="multipleConfig?.navigationFontColor"
      type="small"
    />
  </el-form-item>
</template>
