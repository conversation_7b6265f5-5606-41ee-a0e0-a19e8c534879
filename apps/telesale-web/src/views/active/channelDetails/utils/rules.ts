const rulesProps = {
  name: [
    {
      required: true,
      message: "场景名称不能为空",
      trigger: "blur"
    }
  ],
  channelGroup: [
    {
      required: true,
      message: "请选择活码场景类型",
      trigger: "change"
    }
  ],
  accountId: [
    {
      required: true,
      message: "请选择企微账号",
      trigger: "change"
    }
  ],
  ruleTemplateId: [
    {
      required: true,
      message: "请选择分配规则",
      trigger: "change"
    }
  ],
  clueInvalidDay: [
    {
      required: true,
      message: "请选择线索时长",
      trigger: "change"
    }
  ],
  welcomeMsgId: [
    {
      required: true,
      message: "请选择时段欢迎语",
      trigger: "change"
    }
  ],
  fetchCustomerType: [
    {
      required: true,
      message: "请选择活码类型",
      trigger: "change"
    }
  ],
  miniProgramId: [
    {
      required: true,
      message: "请选择小程序",
      trigger: "change"
    }
  ],
  firstConfig: [
    {
      required: true,
      message: "请选择首次活码主题",
      trigger: "change"
    }
  ]
};

export default rulesProps;
