/**
 * @Date         : 2025-01-27 16:30:00
 * @Description  : 聊天功能Hook - 支持流式TTS音频播放
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
 */

import { ref } from "vue";
import { ElMessage } from "element-plus";
import {
  workerTrainingTaskCourseChatStream,
  sendTts,
  sendTtsStream
} from "/@/api/AIQualityInspection/taskManagement";
import { useForbiddenWordCheck } from "./useForbiddenWordCheck";

/**
 * 消息接口
 */
interface Message {
  content: string;
  isBot: boolean;
  index: string;
  /** 消息开始时间（秒级时间戳） - 用户消息为录音开始时间，机器人消息为TTS开始时间 */
  startTime?: number;
  /** 消息发送时是否处于超时状态 */
  wasTimeout?: boolean;
}

/**
 * 角色信息接口
 */
interface RoleInfo {
  role: string;
  roleName: string;
  roleIntroduction: string;
  personality: string;
}

/**
 * 角色映射函数 - 将中文角色名映射为API所需的英文角色名
 * @param role 中文角色名
 * @returns API所需的英文角色名
 */
function mapRoleToApiRole(role: string): string {
  const roleMap: Record<string, string> = {
    孩子爸爸: "father",
    孩子妈妈: "mother"
  };

  return roleMap[role] || role;
}

/**
 * 聊天Hook
 * @returns 聊天相关的状态和方法
 */
export function useChat() {
  // 状态变量
  const messages = ref<Message[]>([]);
  const isAiResponding = ref(false);
  const audioData = ref<Map<string, ArrayBuffer>>(new Map());

  // 违禁词检测hooks
  const forbiddenWordCheck = useForbiddenWordCheck();

  // 新增：TTS等待状态管理
  const isTtsWaiting = ref(false); // TTS接口是否正在等待返回
  const currentTtsMessageId = ref<string | null>(null); // 当前等待TTS的消息ID

  // 事件回调
  const onShowLoading = ref<() => void>(() => {});
  const onHideLoading = ref<() => void>(() => {});
  const onScrollToBottom = ref<() => void>(() => {});
  const onStartStreamAudio = ref<(msgId: string) => void>(() => {});
  const onHandleStreamAudioChunk = ref<(audioChunk: ArrayBuffer) => void>(
    () => {}
  );
  const onCompleteStreamAudio = ref<() => void>(() => {});
  const onCompleteStreamAudioPlayback = ref<(msgId: string) => void>(() => {}); // 新增：音频播放完全结束回调
  const onForceStopStreamAudio = ref<() => void>(() => {});
  const onPlayAudio = ref<(message: Message) => void>(() => {});
  const onStopAudio = ref<() => void>(() => {});

  // 新增：AI状态变化回调
  const onAiStateChange = ref<(isResponding: boolean) => void>(() => {});

  // 流式TTS状态管理
  const streamTtsState = ref<
    Map<
      string,
      {
        isStarted: boolean;
        isCompleted: boolean;
        audioBuffers: ArrayBuffer[];
        isStreamPlaying: boolean; // 新增：是否正在流式播放（而非缓存重播）
      }
    >
  >(new Map());

  /**
   * 设置聊天区域回调函数
   * @param callbacks 回调函数对象
   */
  function setChatAreaCallbacks(callbacks: {
    onShowLoading?: () => void;
    onHideLoading?: () => void;
    onScrollToBottom?: () => void;
    onStartStreamAudio?: (msgId: string) => void;
    onHandleStreamAudioChunk?: (audioChunk: ArrayBuffer) => void;
    onCompleteStreamAudio?: () => void;
    onCompleteStreamAudioPlayback?: (msgId: string) => void; // 新增：音频播放完全结束回调
    onForceStopStreamAudio?: () => void;
    onPlayAudio?: (message: Message) => void;
    onStopAudio?: () => void;
    onAiStateChange?: (isResponding: boolean) => void; // 新增：AI状态变化回调
  }) {
    if (callbacks.onShowLoading) onShowLoading.value = callbacks.onShowLoading;
    if (callbacks.onHideLoading) onHideLoading.value = callbacks.onHideLoading;
    if (callbacks.onScrollToBottom)
      onScrollToBottom.value = callbacks.onScrollToBottom;
    if (callbacks.onStartStreamAudio)
      onStartStreamAudio.value = callbacks.onStartStreamAudio;
    if (callbacks.onHandleStreamAudioChunk)
      onHandleStreamAudioChunk.value = callbacks.onHandleStreamAudioChunk;
    if (callbacks.onCompleteStreamAudio)
      onCompleteStreamAudio.value = callbacks.onCompleteStreamAudio;
    if (callbacks.onCompleteStreamAudioPlayback)
      onCompleteStreamAudioPlayback.value =
        callbacks.onCompleteStreamAudioPlayback;
    if (callbacks.onForceStopStreamAudio)
      onForceStopStreamAudio.value = callbacks.onForceStopStreamAudio;
    if (callbacks.onPlayAudio) onPlayAudio.value = callbacks.onPlayAudio;
    if (callbacks.onStopAudio) onStopAudio.value = callbacks.onStopAudio;
    if (callbacks.onAiStateChange)
      onAiStateChange.value = callbacks.onAiStateChange;
  }

  /**
   * 发送消息 - 支持流式响应处理和流式TTS播放
   * @param content 消息内容
   * @param workerTrainingTaskId 工作任务ID
   * @param trainingTaskCourseId 课程ID
   * @param roleInfo 角色信息
   * @param userMessageStartTime 用户消息开始时间（录音开始时间，秒级时间戳）
   * @param userMessageId 用户消息ID（来自录音时的msgid，确保与WebSocket ASR的msgid一致）
   * @param isTextOnlyMode 是否为纯文本模式
   * @param wasTimeout 发送消息时是否处于超时状态
   */
  async function sendMessage(
    content: string,
    workerTrainingTaskId: number,
    trainingTaskCourseId: number,
    roleInfo: RoleInfo,
    userMessageStartTime?: number,
    userMessageId?: string,
    isTextOnlyMode: boolean = false,
    wasTimeout: boolean = false
  ) {
    if (!content.trim() || isAiResponding.value) return;

    // 用户输入完成后立即异步检查违禁词（不阻塞后续流程）
    forbiddenWordCheck.checkForbiddenWordsAsync(content);

    // 使用传入的消息ID或生成新的唯一消息ID
    const msgId = userMessageId || crypto.randomUUID();

    // 添加用户消息
    messages.value.push({
      content: content,
      isBot: false,
      index: msgId,
      startTime: userMessageStartTime || Math.floor(Date.now() / 1000), // 使用传入的录音开始时间或当前时间
      wasTimeout: wasTimeout // 记录发送时的超时状态
    });

    // 滚动到底部
    onScrollToBottom.value();

    // 设置AI响应状态
    isAiResponding.value = true;
    // 通知状态变化
    onAiStateChange.value(true);

    // 生成AI回复消息ID
    const botMsgId = crypto.randomUUID();

    // 添加空的AI消息占位符（暂时不设置startTime，在TTS开始时设置）
    messages.value.push({
      content: "",
      isBot: true,
      index: botMsgId
    });

    // 显示加载状态
    onShowLoading.value();
    onScrollToBottom.value();

    try {
      // 准备对话上下文
      const chatContext = messages.value
        .filter(msg => msg.index !== msgId && msg.index !== botMsgId) // 排除当前消息和AI占位符
        .map(msg => ({
          role: msg.isBot ? "bot" : "saler",
          content: msg.content
        }));

      let aiResponseContent = "";
      let isStreamFinished = false;

      // 调用流式AI接口
      await workerTrainingTaskCourseChatStream(
        {
          workerTrainingTaskId: workerTrainingTaskId,
          trainingTaskCourseId: trainingTaskCourseId,
          question: content,
          chatContext: chatContext
        },
        (chunk: string) => {
          // 处理流式数据块
          const result = processStreamChunk(chunk, botMsgId);
          if (result.content) {
            aiResponseContent = result.content;
          }
          if (result.finished) {
            isStreamFinished = true;
          }
        }
      );

      // AI聊天完全返回后，调用流式TTS
      if (isStreamFinished && aiResponseContent.trim()) {
        console.log("AI聊天完成，开始调用流式TTS:", {
          botMsgId,
          contentLength: aiResponseContent.length,
          isTextOnlyMode
        });

        // 隐藏加载状态（在开始TTS前）
        onHideLoading.value();

        // 重置AI响应状态（在开始TTS前）
        isAiResponding.value = false;
        // 通知状态变化
        onAiStateChange.value(false);

        await startStreamTTS(
          botMsgId,
          aiResponseContent,
          roleInfo,
          isTextOnlyMode
        );
      } else if (!aiResponseContent.trim()) {
        // 如果没有收到任何内容，设置默认消息
        const messageIndex = messages.value.findIndex(
          msg => msg.index === botMsgId
        );
        if (messageIndex !== -1) {
          messages.value[messageIndex].content =
            "抱歉，我暂时无法回答这个问题。";
        }

        // 隐藏加载状态
        onHideLoading.value();

        // 重置AI响应状态
        isAiResponding.value = false;
        // 通知状态变化
        onAiStateChange.value(false);
      }
    } catch (error) {
      console.error("AI对话请求失败:", error);

      // 根据错误类型显示不同的错误信息
      let errorMessage = "AI对话请求失败，请重试";
      if (error.message) {
        // 如果是我们自定义的友好错误信息，直接使用
        if (
          error.message.includes("请求超时") ||
          error.message.includes("网络连接失败") ||
          error.message.includes("认证失败") ||
          error.message.includes("权限不足") ||
          error.message.includes("服务器内部错误")
        ) {
          errorMessage = error.message;
        }
      }

      ElMessage.error(errorMessage);

      // 更新为错误消息
      const messageIndex = messages.value.findIndex(
        msg => msg.index === botMsgId
      );
      if (messageIndex !== -1) {
        messages.value[messageIndex].content =
          "抱歉，系统暂时无法处理您的请求，请稍后再试。";
      }

      // 滚动到底部
      onScrollToBottom.value();

      // 隐藏加载状态
      onHideLoading.value();

      // 重置AI响应状态
      isAiResponding.value = false;
      // 通知状态变化
      onAiStateChange.value(false);
    }
  }

  /**
   * 处理流式数据块
   * @param chunk 数据块
   * @param botMsgId AI消息ID
   * @returns 处理结果 { content: 当前完整内容, finished: 是否完成 }
   */
  function processStreamChunk(
    chunk: string,
    botMsgId: string
  ): { content: string; finished: boolean } {
    try {
      // 处理Server-Sent Events格式
      if (chunk.startsWith("data: ")) {
        const dataStr = chunk.substring(6).trim();

        // 检查是否为结束标记
        if (dataStr === "[DONE]" || dataStr === "EOF" || dataStr === "") {
          return { content: "", finished: true };
        }

        let content = "";

        // 尝试解析为JSON
        try {
          const data = JSON.parse(dataStr);

          // 根据实际的响应格式调整这里的解析逻辑
          if (data.choices && data.choices[0] && data.choices[0].delta) {
            content = data.choices[0].delta.content || "";
          } else if (data.content) {
            content = data.content;
          } else if (data.answer) {
            content = data.answer;
          } else if (typeof data === "string") {
            content = data;
          }
        } catch (jsonError) {
          // 如果不是JSON格式，检查是否为EOF终止符
          if (dataStr === "EOF") {
            return { content: "", finished: true };
          }
          // 直接使用原始文本内容
          content = dataStr;
        }

        if (content && content !== "EOF") {
          // 更新AI消息内容
          const messageIndex = messages.value.findIndex(
            msg => msg.index === botMsgId
          );
          if (messageIndex !== -1) {
            messages.value[messageIndex].content += content;

            // 滚动到底部
            onScrollToBottom.value();

            return {
              content: messages.value[messageIndex].content,
              finished: false
            };
          }
        }
      } else {
        // 处理非SSE格式的数据

        // 检查是否为EOF终止符
        if (chunk.trim() === "EOF") {
          return { content: "", finished: true };
        }

        try {
          // 尝试解析为JSON
          const data = JSON.parse(chunk);
          if (data.answer || data.content) {
            const content = data.answer || data.content;
            const messageIndex = messages.value.findIndex(
              msg => msg.index === botMsgId
            );
            if (messageIndex !== -1) {
              messages.value[messageIndex].content = content;

              // 滚动到底部
              onScrollToBottom.value();

              return { content: content, finished: false };
            }
          }
        } catch (jsonError) {
          // 如果不是JSON格式，直接作为文本内容处理
          if (chunk.trim() && chunk.trim() !== "EOF") {
            const messageIndex = messages.value.findIndex(
              msg => msg.index === botMsgId
            );
            if (messageIndex !== -1) {
              messages.value[messageIndex].content += chunk;

              // 滚动到底部
              onScrollToBottom.value();

              return {
                content: messages.value[messageIndex].content,
                finished: false
              };
            }
          }
        }
      }
    } catch (error) {
      console.warn("Failed to parse stream chunk:", chunk, error);
    }

    return { content: "", finished: false };
  }

  /**
   * 启动流式TTS - 在AI聊天完全返回后调用
   * @param msgId 消息ID
   * @param finalContent 完整的AI回复内容
   * @param roleInfo 角色信息
   * @param isTextOnlyMode 是否为纯文本模式
   */
  async function startStreamTTS(
    msgId: string,
    finalContent: string,
    roleInfo: RoleInfo,
    isTextOnlyMode: boolean = false
  ) {
    console.log("启动流式TTS:", {
      msgId,
      contentLength: finalContent.length,
      role: roleInfo.role,
      isTextOnlyMode
    });

    // 设置机器人消息的TTS开始时间
    const ttsStartTime = Math.floor(Date.now() / 1000);
    const messageIndex = messages.value.findIndex(msg => msg.index === msgId);
    if (messageIndex !== -1) {
      messages.value[messageIndex].startTime = ttsStartTime;
      console.log("设置机器人消息TTS开始时间:", { msgId, ttsStartTime });
    }

    // 如果是纯文本模式，跳过TTS和音频播放
    if (isTextOnlyMode) {
      // 在纯文本模式下，不需要设置TTS等待状态，直接标记为完成
      // 这样可以确保AI回答完成后能立即触发自动完成逻辑
      completeStreamAudioPlayback(msgId);
      return;
    }

    // 设置TTS等待状态
    isTtsWaiting.value = true;
    currentTtsMessageId.value = msgId;

    // 清理之前可能存在的状态
    streamTtsState.value.delete(msgId);

    // 初始化流式TTS状态
    streamTtsState.value.set(msgId, {
      isStarted: true,
      isCompleted: false,
      audioBuffers: [],
      isStreamPlaying: true
    });

    // 通知开始流式音频（这会触发ChatArea的startStreamAudio方法）
    onStartStreamAudio.value(msgId);

    try {
      // 调用流式TTS接口，传入完整内容
      await sendTtsStream(
        {
          text: finalContent,
          role: mapRoleToApiRole(roleInfo.role),
          msgid: msgId
        },
        (audioChunk: ArrayBuffer) => {
          // 处理每个音频buffer - 先缓存数据，再通过ChatArea播放
          handleStreamAudioChunk(msgId, audioChunk);
          // 通过ChatArea的回调处理播放
          onHandleStreamAudioChunk.value(audioChunk);
        }
      );

      // 标记TTS流式返回完成（但不清理等待状态）
      const state = streamTtsState.value.get(msgId);
      if (state) {
        state.isCompleted = true;
        // 注意：不在这里设置 isStreamPlaying = false，等音频播放完成后再设置
        streamTtsState.value.set(msgId, state);

        // 合并所有音频数据用于重播
        if (state.audioBuffers.length > 0) {
          const mergedAudioBuffer = mergeArrayBuffers(state.audioBuffers);
          audioData.value.set(msgId, mergedAudioBuffer);
          console.log("流式TTS音频缓存成功:", {
            msgId,
            totalChunks: state.audioBuffers.length,
            mergedSize: mergedAudioBuffer.byteLength,
            cacheSize: audioData.value.size
          });
        } else {
          console.warn("流式TTS没有音频数据可缓存:", msgId);
        }

        // 通知流式音频完成（这会触发ChatArea的completeStreamAudio方法）
        onCompleteStreamAudio.value();

        console.log("流式TTS接口返回完成:", {
          msgId,
          totalBuffers: state.audioBuffers.length
        });
      }
    } catch (error) {
      console.error("流式TTS失败:", error);
      // 通知强制停止流式音频
      onForceStopStreamAudio.value();
      // 降级到传统TTS
      await fallbackToTraditionalTTS(
        msgId,
        finalContent,
        roleInfo,
        isTextOnlyMode
      );
    }
    // 注意：不在这里清理TTS等待状态，等音频播放完成后再清理
  }

  /**
   * 处理流式音频数据块 - TTS返回的每个buffer（仅存储，不播放）
   * @param msgId 消息ID
   * @param audioChunk 音频数据块
   */
  function handleStreamAudioChunk(msgId: string, audioChunk: ArrayBuffer) {
    const state = streamTtsState.value.get(msgId);
    if (!state) {
      console.warn("没有找到对应的TTS状态:", msgId);
      return;
    }

    console.log("接收到TTS音频数据块:", {
      msgId,
      chunkSize: audioChunk.byteLength,
      totalChunks: state.audioBuffers.length + 1
    });

    // 存储音频数据块（用于重播和合并）
    state.audioBuffers.push(audioChunk);
    streamTtsState.value.set(msgId, state);

    // 注意：播放由ChatArea组件统一管理，这里只负责数据存储
  }

  /**
   * 完成流式音频播放 - 在音频播放完全结束时调用
   * @param msgId 消息ID
   */
  function completeStreamAudioPlayback(msgId: string) {
    console.log("流式音频播放完全结束:", msgId);

    const state = streamTtsState.value.get(msgId);
    if (state) {
      // 标记流式播放结束
      state.isStreamPlaying = false;
      streamTtsState.value.set(msgId, state);
    }

    // 清理TTS等待状态
    if (currentTtsMessageId.value === msgId) {
      isTtsWaiting.value = false;
      currentTtsMessageId.value = null;
      console.log("清理TTS等待状态:", msgId);
    }
  }

  /**
   * 降级到传统TTS
   * @param msgId 消息ID
   * @param content 内容
   * @param roleInfo 角色信息
   * @param isTextOnlyMode 是否为纯文本模式
   */
  async function fallbackToTraditionalTTS(
    msgId: string,
    content: string,
    roleInfo: RoleInfo,
    isTextOnlyMode: boolean = false
  ) {
    console.log("降级到传统TTS:", {
      msgId,
      contentLength: content.length,
      isTextOnlyMode
    });

    // 如果是纯文本模式，跳过TTS和音频播放
    if (isTextOnlyMode) {
      console.log("纯文本模式，跳过传统TTS:", msgId);
      // 清理流式TTS状态
      streamTtsState.value.delete(msgId);
      // 直接标记为完成
      completeStreamAudioPlayback(msgId);
      return;
    }

    try {
      // 设置机器人消息的TTS开始时间（如果还没有设置）
      const messageIndex = messages.value.findIndex(msg => msg.index === msgId);
      if (messageIndex !== -1 && !messages.value[messageIndex].startTime) {
        const ttsStartTime = Math.floor(Date.now() / 1000);
        messages.value[messageIndex].startTime = ttsStartTime;
        console.log("设置机器人消息TTS开始时间（传统TTS）:", {
          msgId,
          ttsStartTime
        });
      }

      // 设置TTS等待状态（如果还没有设置）
      if (!isTtsWaiting.value) {
        isTtsWaiting.value = true;
        currentTtsMessageId.value = msgId;
      }

      const audioBuffer = await sendTts({
        text: content,
        role: mapRoleToApiRole(roleInfo.role),
        msgid: msgId
      });

      if (audioBuffer && audioBuffer instanceof ArrayBuffer) {
        audioData.value.set(msgId, audioBuffer);

        // 播放音频
        onPlayAudio.value({
          content,
          isBot: true,
          index: msgId
        });

        console.log("传统TTS播放成功");
      }
    } catch (error) {
      console.error("传统TTS也失败:", error);
      ElMessage.error("语音生成失败，请重试");
    } finally {
      // 清理流式TTS状态
      streamTtsState.value.delete(msgId);
      // 使用新的状态清理方法
      completeStreamAudioPlayback(msgId);
    }
  }

  /**
   * 合并ArrayBuffer数组
   * @param buffers ArrayBuffer数组
   * @returns 合并后的ArrayBuffer
   */
  function mergeArrayBuffers(buffers: ArrayBuffer[]): ArrayBuffer {
    const totalLength = buffers.reduce((sum, buf) => sum + buf.byteLength, 0);
    const mergedBuffer = new ArrayBuffer(totalLength);
    const mergedUint8 = new Uint8Array(mergedBuffer);
    let offset = 0;

    for (const buf of buffers) {
      const sourceUint8 = new Uint8Array(buf);
      mergedUint8.set(sourceUint8, offset);
      offset += sourceUint8.byteLength;
    }

    return mergedBuffer;
  }

  /**
   * 停止所有音频播放
   */
  function stopAllAudio() {
    console.log("停止所有音频播放");

    // 通过ChatArea组件停止音频
    onStopAudio.value();

    // 清理流式TTS状态
    streamTtsState.value.clear();
  }

  /**
   * 清理聊天数据
   */
  function clearChat() {
    // 停止所有音频播放
    stopAllAudio();

    messages.value = [];
    audioData.value.clear();
    streamTtsState.value.clear();
    isAiResponding.value = false;

    // 清理TTS等待状态
    isTtsWaiting.value = false;
    currentTtsMessageId.value = null;

    // 清理违禁词提示状态
    forbiddenWordCheck.clearForbiddenWordMessage();

    console.log("聊天数据已清理，包括TTS状态和违禁词提示");
  }

  return {
    // 状态
    messages,
    isAiResponding,
    audioData,
    streamTtsState,
    // 新增：TTS等待状态
    isTtsWaiting,
    currentTtsMessageId,

    // 方法
    sendMessage,
    setChatAreaCallbacks,
    stopAllAudio,
    clearChat,
    completeStreamAudioPlayback, // 新增：完成流式音频播放方法

    // 违禁词检测实例
    forbiddenWordCheck,

    // 新增：状态查询方法
    /**
     * 检查指定消息是否正在流式播放（而非缓存重播）
     * @param messageId 消息ID
     * @returns 是否正在流式播放
     */
    isMessageStreamPlaying: (messageId: string) => {
      const state = streamTtsState.value.get(messageId);
      return state?.isStreamPlaying || false;
    },

    /**
     * 检查是否应该禁用播放按钮
     * @param messageId 消息ID
     * @param isCurrentPlaying 是否是当前播放的消息
     * @returns 是否应该禁用
     */
    shouldDisablePlayButton: (messageId: string, isCurrentPlaying: boolean) => {
      // 如果TTS正在等待返回，禁用所有播放按钮
      if (isTtsWaiting.value) {
        return true;
      }

      // 如果当前消息正在流式播放，禁用暂停按钮
      if (isCurrentPlaying) {
        const state = streamTtsState.value.get(messageId);
        return state?.isStreamPlaying || false;
      }

      return false;
    }
  };
}
